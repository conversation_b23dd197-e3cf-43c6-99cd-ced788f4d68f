use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, SocketAddr};
use std::sync::Arc;
use std::time::Duration;

use log::debug;
use socket2::SockRef;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::{TcpStream, UdpSocket};
use tokio::spawn;
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio::sync::RwLock;
use tokio::task::Join<PERSON><PERSON>le;
use tokio::time::{sleep, Instant};

use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};

use crate::route_table::route_table::RouteTable;
use crate::server::server_handler::ServerHandler;

// 常量定义
/// UDP 数据包最大缓冲区大小
const UDP_BUFFER_SIZE: usize = 2048;
/// TCP 数据包最大缓冲区大小
const TCP_BUFFER_SIZE: usize = 16384;
/// UDP 连接超时时间（5分钟）
const UDP_TIMEOUT_SECS: u64 = 60 * 5;
/// TCP 连接超时时间（2小时）
const TCP_TIMEOUT_SECS: u64 = 60 * 60 * 2;
/// 原生UDP协议标识头
const NATIVE_UDP_HEADER: [u8; 2] = [0x0f, 0x2f];

/// 网络路由结构体，用于处理TCP或UDP协议的网络连接
///
/// 该结构体负责管理网络连接的生命周期，包括：
/// - TCP/UDP数据转发
/// - 连接状态管理
/// - 超时检测
/// - 资源清理
pub struct Route {
    /// 协议类型（TCP/UDP/NativeUdp）
    pub(crate) protocol: PackageProtocol,
    /// 源地址
    pub(crate) source_addr: String,
    /// 目标地址
    pub(crate) target_addr: String,
    /// 服务器处理器，用于与客户端通信
    server_handler: RwLock<Option<Arc<ServerHandler>>>,
    /// TCP数据发送通道的发送端
    send_data_sender: UnboundedSender<Vec<u8>>,
    /// TCP数据发送通道的接收端
    send_data_receiver: RwLock<Option<UnboundedReceiver<Vec<u8>>>>,
    /// 路由表引用
    route_table: Arc<RouteTable>,
    /// TCP读取任务句柄
    tcp_read_job: RwLock<Option<JoinHandle<()>>>,
    /// TCP写入任务句柄
    tcp_write_job: Arc<RwLock<Option<JoinHandle<()>>>>,
    /// UDP读取任务句柄（用于UDP over TCP）
    udp_read_job: Option<JoinHandle<()>>,
    /// UDP套接字（用于UDP over TCP）
    udp_socket: RwLock<Option<Arc<UdpSocket>>>,
    /// 原生UDP读取任务句柄
    native_udp_read_job: Option<JoinHandle<()>>,
    /// 原生UDP套接字
    native_udp_socket: RwLock<Option<Arc<UdpSocket>>>,
    /// 原生UDP客户端地址
    native_udp_socket_addr: Arc<RwLock<Option<SocketAddr>>>,
    /// 最后活跃时间，用于超时检测
    active_time: Arc<RwLock<Instant>>,
}

impl Route {
    /// 创建新的网络路由
    ///
    /// # 参数
    /// * `protocol` - 协议类型（TCP/UDP/NativeUdp）
    /// * `target_addr` - 目标地址
    /// * `source_addr` - 源地址
    /// * `server_handler` - 服务器处理器
    /// * `route_table` - 路由表引用
    ///
    /// # 返回值
    /// 返回配置好的Route实例
    pub async fn new(
        protocol: PackageProtocol,
        target_addr: String,
        source_addr: String,
        server_handler: Arc<ServerHandler>,
        route_table: Arc<RouteTable>
    ) -> Route {
        // 初始化活跃时间和其他路由参数
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();
        let source_addr_clone = source_addr.clone();
        let server_handler_clone = server_handler.clone();
        let mut udp_read_job_opt = None;
        let mut udp_socket_opt = None;
        let mut native_udp_read_job_opt = None;
        let mut native_udp_socket_opt = None;
        let native_udp_socket_addr = Arc::new(RwLock::new(None));

        // 处理UDP over TCP协议
        if protocol == PackageProtocol::UDP {
            if let Ok(udp_socket) = UdpSocket::bind("0.0.0.0:0").await {
                let arc_udp_socket = Arc::new(udp_socket);
                let arc_udp_socket_clone = arc_udp_socket.clone();
                udp_socket_opt = Some(arc_udp_socket.clone());

                // 启动UDP数据包处理任务
                udp_read_job_opt = Some(spawn(async move {
                    let mut buf = [0u8; UDP_BUFFER_SIZE];
                    loop {
                        match arc_udp_socket_clone.recv_from(&mut buf).await {
                            Ok((n, source_addr)) => {
                                if n == 0 {
                                    debug!("UDP socket received 0 bytes, closing connection");
                                    break;
                                }
                                // 更新活跃时间
                                *active_time_clone.write().await = Instant::now();

                                // 创建隧道数据包并发送给客户端
                                let mut tunnel_package = TunnelPackage::new(
                                    PackageCmd::TData,
                                    PackageProtocol::UDP,
                                    Some(source_addr_clone.clone()),
                                    Some(source_addr.to_string()),
                                    Some(buf[..n].to_vec()),
                                );
                                if !server_handler_clone.write_tunnel_package(&mut tunnel_package).await {
                                    debug!("Failed to write tunnel package, closing UDP connection");
                                    break;
                                }
                            }
                            Err(e) => {
                                debug!("UDP recv_from error: {}, closing connection", e);
                                break;
                            }
                        }
                    }
                }));
            } else {
                debug!("Failed to bind UDP socket for UDP over TCP");
            }
        }
        // 处理原生UDP协议
        else if protocol == PackageProtocol::NativeUdp {
            if let Ok(udp_socket) = UdpSocket::bind("0.0.0.0:0").await {
                // 获取本地端口并发送给客户端
                if let Ok(local_addr) = udp_socket.local_addr() {
                    let mut tunnel_package = TunnelPackage::new(
                        PackageCmd::TData,
                        PackageProtocol::NativeUdp,
                        Some(source_addr_clone.clone()),
                        Some(format!("{}", local_addr.port())),
                        None,
                    );
                    server_handler_clone.write_tunnel_package(&mut tunnel_package).await;
                    debug!("Native UDP server bound to port: {}", local_addr.port());
                }

                let arc_udp_socket = Arc::new(udp_socket);
                let arc_udp_socket_clone = arc_udp_socket.clone();
                let native_udp_socket_addr_clone = native_udp_socket_addr.clone();
                udp_socket_opt = Some(arc_udp_socket.clone());

                // 启动原生UDP处理
                if let Ok((native_udp_socket, native_udp_socket_job)) = Self::start_native_udp(
                    arc_udp_socket_clone.clone(),
                    active_time_clone.clone(),
                    native_udp_socket_addr.clone(),
                    server_handler_clone.clone()
                ).await {
                    native_udp_socket_opt = Some(native_udp_socket.clone());
                    native_udp_read_job_opt = Some(native_udp_socket_job);

                    // 启动客户端数据接收任务
                    udp_read_job_opt = Some(spawn(async move {
                        let mut buf = [0u8; UDP_BUFFER_SIZE];
                        let mut package_buf = vec![];

                        loop {
                            match arc_udp_socket_clone.recv_from(&mut buf).await {
                                Ok((n, source_addr)) => {
                                    // 记录客户端地址
                                    *native_udp_socket_addr_clone.write().await = Some(source_addr);
                                    // 更新活跃时间
                                    *active_time_clone.write().await = Instant::now();
                                    if n == 0 {
                                        debug!("Native UDP received 0 bytes, closing connection");
                                        break;
                                    }

                                    // 统计上传流量
                                    server_handler_clone.add_user_traffic_upload(n).await;

                                    if n > 2 {
                                        if buf[..2] == [0x0f, 0x2f] {
                                            package_buf.clear();
                                        }
                                    }
                                    package_buf.extend_from_slice(&buf[..n]);

                                   
                                    // 解析隧道数据包
                                    match TunnelPackage::from_byte_array(&mut package_buf) {
                                        Ok(Some(package)) => {
                                            if let (Some(target_address), Some(data)) =
                                                (package.target_address, package.data) {
                                                // 转发数据到目标地址
                                                if let Err(e) = native_udp_socket.send_to(&data, target_address).await {
                                                    debug!("Failed to send native UDP data: {}", e);
                                                    break;
                                                }
                                            }
                                        }
                                        Ok(None) => {
                                            // 数据包不完整，继续接收
                                            continue;
                                        }
                                        Err(e) => {
                                            debug!("Failed to parse tunnel package: {}", e);
                                        }
                                    }
                                }
                                Err(e) => {
                                    debug!("Native UDP recv_from error: {}", e);
                                    break;
                                }
                            }
                        }
                    }));
                } else {
                    debug!("Failed to start native UDP handler");
                }
            } else {
                debug!("Failed to bind UDP socket for native UDP");
            }
        }

        // 创建TCP数据传输通道
        let channel = tokio::sync::mpsc::unbounded_channel::<Vec<u8>>();

        Route {
            protocol,
            source_addr,
            target_addr,
            server_handler: RwLock::new(Some(server_handler)),
            send_data_sender: channel.0,
            send_data_receiver: RwLock::new(Some(channel.1)),
            route_table,
            tcp_read_job: RwLock::new(None),
            tcp_write_job: Arc::new(RwLock::new(None)),
            udp_read_job: udp_read_job_opt,
            udp_socket: RwLock::new(udp_socket_opt),
            native_udp_read_job: native_udp_read_job_opt,
            native_udp_socket: RwLock::new(native_udp_socket_opt),
            native_udp_socket_addr,
            active_time,
        }
    }

    /// 启动原生UDP处理器
    ///
    /// 创建一个新的UDP套接字用于与目标服务器通信，并启动数据转发任务
    ///
    /// # 参数
    /// * `server_udp_socket` - 与客户端通信的UDP套接字
    /// * `active_time_clone` - 活跃时间引用
    /// * `socket_addr` - 客户端地址引用
    /// * `server_handler` - 服务器处理器
    ///
    /// # 返回值
    /// 返回目标UDP套接字和处理任务句柄，或错误信息
    async fn start_native_udp(
        server_udp_socket: Arc<UdpSocket>,
        active_time_clone: Arc<RwLock<Instant>>,
        socket_addr: Arc<RwLock<Option<SocketAddr>>>,
        server_handler: Arc<ServerHandler>
    ) -> Result<(Arc<UdpSocket>, JoinHandle<()>), String> {
        match UdpSocket::bind("0.0.0.0:0").await {
            Ok(udp_socket) => {
                let arc_udp_socket = Arc::new(udp_socket);
                let arc_udp_socket_clone = arc_udp_socket.clone();

                // 启动目标服务器数据接收任务
                let udp_read_job = spawn(async move {
                    let mut buf = [0u8; UDP_BUFFER_SIZE];
                    loop {
                        match arc_udp_socket_clone.recv_from(&mut buf).await {
                            Ok((n, source_addr)) => {
                                if n == 0 {
                                    debug!("Native UDP target socket received 0 bytes");
                                    break;
                                }

                                // 统计下载流量
                                server_handler.add_user_traffic_download(n).await;

                                // 创建隧道数据包
                                let mut send_data = vec![];
                                TunnelPackage::new(
                                    PackageCmd::TData,
                                    PackageProtocol::NativeUdp,
                                    Some(source_addr.to_string()),
                                    None,
                                    Some(buf[..n].to_vec()),
                                ).to_byte_array(&mut send_data);

                                // 转发数据给客户端
                                if let Some(target_addr) = socket_addr.read().await.as_ref() {
                                    if let Err(e) = server_udp_socket.send_to(&send_data, target_addr).await {
                                        debug!("Failed to send data to client: {}", e);
                                    }
                                }

                                // 更新活跃时间
                                *active_time_clone.write().await = Instant::now();
                            }
                            Err(e) => {
                                debug!("Native UDP target recv_from error: {}", e);
                                break;
                            }
                        }
                    }
                });

                Ok((arc_udp_socket, udp_read_job))
            }
            Err(e) => {
                Err(format!("Failed to bind native UDP socket: {}", e))
            }
        }
    }

    /// 发送TCP数据到目标地址
    ///
    /// # 参数
    /// * `data` - 要发送的数据
    pub async fn send_tcp_data_to_target(&self, data: Vec<u8>) {
        if self.protocol == PackageProtocol::TCP {
            if let Err(e) = self.send_data_sender.send(data) {
                debug!("Failed to send TCP data through channel: {}", e);
            }
        }
    }

    /// 发送UDP数据到目标地址
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `data` - 要发送的数据
    pub async fn send_udp_data_to_target(&self, target_addr: String, data: Vec<u8>) {
        if self.protocol == PackageProtocol::UDP {
            if let Some(udp_socket) = self.udp_socket.read().await.as_ref() {
                // 更新活跃时间
                *self.active_time.write().await = Instant::now();

                // 发送UDP数据
                if let Err(e) = udp_socket.send_to(data.as_slice(), &target_addr).await {
                    debug!("Failed to send UDP data to {}: {}", target_addr, e);
                }
            }
        }
    }

    /// 连接到目标地址并处理TCP通信
    ///
    /// 建立到目标服务器的TCP连接，并启动读写任务进行数据转发
    pub async fn connect(&self) {
        let route_table = self.route_table.clone();
        let target_addr = self.target_addr.clone();
        let source_addr = self.source_addr.clone();
        let protocol = self.protocol.clone();
        let server_handler = self.server_handler.read().await.clone();
        let mut send_data_receiver = self.send_data_receiver.write().await.take().unwrap();
        let route_table_clone = route_table.clone();
        let tcp_write_job = self.tcp_write_job.clone();

        // 启动TCP通信处理任务
        let _ = self.tcp_read_job.write().await.insert(spawn(async move {
            match TcpStream::connect(&target_addr).await {
                Ok(server_stream) => {
                    // 配置TCP连接参数
                    if let Err(e) = server_stream.set_nodelay(true) {
                        debug!("Failed to set TCP nodelay: {}", e);
                    }
                    let sock_ref = SockRef::from(&server_stream);
                    if let Err(e) = sock_ref.set_keepalive(true) {
                        debug!("Failed to set TCP keepalive: {}", e);
                    }

                    debug!("TCP连接已建立: {}", target_addr);
                    let (mut server_reader, mut server_writer) = server_stream.into_split();

                    // 启动TCP写入任务
                    let source_addr_clone = source_addr.clone();
                    let target_addr_clone = target_addr.clone();
                    let _ = tcp_write_job.write().await.insert(spawn(async move {
                        loop {
                            match send_data_receiver.recv().await {
                                Some(data) => {
                                    if let Err(e) = server_writer.write_all(&data).await {
                                        debug!("TCP写入失败: {}", e);
                                        route_table_clone.remove_route(&source_addr_clone, &target_addr_clone, PackageProtocol::TCP).await;
                                        break;
                                    }
                                }
                                None => {
                                    debug!("TCP写入通道已关闭");
                                    route_table_clone.remove_route(&source_addr_clone, &target_addr_clone, PackageProtocol::TCP).await;
                                    break;
                                }
                            }
                        }
                    }));

                    // TCP读取循环
                    let mut server_buffer = [0u8; TCP_BUFFER_SIZE];
                    loop {
                        match server_reader.read(&mut server_buffer).await {
                            Ok(0) => {
                                debug!("TCP连接已关闭: {}", target_addr);
                                break;
                            }
                            Ok(n) => {
                                // 创建隧道数据包并发送给客户端
                                let mut tunnel_package = TunnelPackage::new(
                                    PackageCmd::TData,
                                    PackageProtocol::TCP,
                                    Some(source_addr.clone()),
                                    Some(target_addr.clone()),
                                    Some(server_buffer[..n].to_vec()),
                                );
                                if let Some(server_handler) = server_handler.as_ref() {
                                    if !server_handler.write_tunnel_package(&mut tunnel_package).await {
                                        debug!("发送隧道数据包失败，关闭连接");
                                        break;
                                    }
                                } else {
                                    debug!("服务器处理器不可用，关闭连接");
                                    break;
                                }
                            }
                            Err(e) => {
                                debug!("TCP读取错误: {}", e);
                                break;
                            }
                        }
                    }
                    // 清理路由
                    route_table.remove_route(&source_addr, &target_addr, protocol).await;
                }
                Err(e) => {
                    debug!("TCP连接失败 {}: {}", target_addr, e);
                    route_table.remove_route(&source_addr, &target_addr, protocol).await;
                }
            }
        }));
    }

    /// 断开路由连接并通知服务器处理器
    ///
    /// 发送关闭连接的隧道数据包，然后清理所有资源
    pub async fn disconnect(&self) {
        let mut tunnel_package = TunnelPackage::new(
            PackageCmd::CloseConnect,
            PackageProtocol::TCP,
            Some(self.source_addr.clone()),
            Some(self.target_addr.clone()),
            None,
        );
        if let Some(server_handler) = self.server_handler.read().await.as_ref() {
            server_handler.write_tunnel_package(&mut tunnel_package).await;
        }
        self.close().await;
    }

    /// 关闭路由，终止所有活跃任务并清理资源
    ///
    /// 该方法会：
    /// - 清理服务器处理器引用
    /// - 关闭所有套接字
    /// - 关闭数据传输通道
    /// - 终止所有后台任务
    pub async fn close(&self) {
        debug!("正在关闭路由: {} -> {}", self.source_addr, self.target_addr);

        // 清理服务器处理器引用
        *self.server_handler.write().await = None;

        // 清理UDP套接字
        if self.udp_socket.write().await.take().is_some() {
            debug!("已清理UDP套接字");
        }

        // 清理原生UDP套接字
        if self.native_udp_socket.write().await.take().is_some() {
            debug!("已清理原生UDP套接字");
        }

        // 关闭数据传输通道
        if let Some(mut receiver) = self.send_data_receiver.write().await.take() {
            receiver.close();
            debug!("已关闭TCP数据传输通道");
        }

        // 终止UDP读取任务
        if let Some(udp_read_job) = self.udp_read_job.as_ref() {
            udp_read_job.abort();
            debug!("已终止UDP读取任务");
        }

        // 终止原生UDP读取任务
        if let Some(native_udp_read_job) = self.native_udp_read_job.as_ref() {
            native_udp_read_job.abort();
            debug!("已终止原生UDP读取任务");
        }

        // 终止TCP写入任务
        if let Some(tcp_write_job) = self.tcp_write_job.write().await.take() {
            tcp_write_job.abort();
            debug!("已终止TCP写入任务");
        }

        // 终止TCP读取任务
        if let Some(tcp_read_job) = self.tcp_read_job.write().await.take() {
            tcp_read_job.abort();
            debug!("已终止TCP读取任务");
        }

        debug!("路由关闭完成: {} -> {}", self.source_addr, self.target_addr);
    }

    /// 检查路由是否超时
    ///
    /// 根据协议类型使用不同的超时时间：
    /// - UDP: 5分钟
    /// - TCP: 2小时
    ///
    /// # 返回值
    /// 如果路由超时返回true，否则返回false
    pub async fn is_timeout(&self) -> bool {
        let now = Instant::now();
        let last_active = *self.active_time.read().await;
        let timeout_duration = match self.protocol {
            PackageProtocol::UDP | PackageProtocol::NativeUdp => {
                Duration::from_secs(UDP_TIMEOUT_SECS)
            }
            PackageProtocol::TCP => {
                Duration::from_secs(TCP_TIMEOUT_SECS)
            }
            _ => {
                // 默认使用TCP超时时间
                Duration::from_secs(TCP_TIMEOUT_SECS)
            }
        };

        let is_timeout = now.duration_since(last_active) > timeout_duration;
        if is_timeout {
            debug!("路由超时: {} -> {} (协议: {:?})",
                   self.source_addr, self.target_addr, self.protocol);
        }
        is_timeout
    }
}
